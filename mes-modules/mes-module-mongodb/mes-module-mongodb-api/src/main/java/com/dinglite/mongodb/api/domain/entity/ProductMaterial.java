package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品物料追溯信息实体类，用于记录生产过程中的物料绑定和消耗数据
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductMaterial对象", description = "产品物料追溯信息")
@Document(collection = "product_material")
public class ProductMaterial implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "MongoDB 文档主键（ObjectId 类型）")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "目标集合名称（固定为 product_material）")
    @Field("collection")
    @JsonProperty("collection")
    private String collection = "product_material";

    @ApiModelProperty(value = "产品序列号")
    @Field("sn")
    @JsonProperty("sn")
    private String sn;

    @ApiModelProperty(value = "产品型号编码")
    @Field("modelCode")
    @JsonProperty("modelCode")
    private String modelCode;

    @ApiModelProperty(value = "生产工单号")
    @Field("productionOrder")
    @JsonProperty("productionOrder")
    private String productionOrder;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "产线编码")
    @Field("lineCode")
    @JsonProperty("lineCode")
    private String lineCode;

    @ApiModelProperty(value = "工序编码")
    @Field("processCode")
    @JsonProperty("processCode")
    private String processCode;

    @ApiModelProperty(value = "工序名称")
    @Field("processName")
    @JsonProperty("processName")
    private String processName;

    @ApiModelProperty(value = "工站名称")
    @Field("stationName")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "工站IP地址")
    @Field("stationIP")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "物料编码")
    @Field("materialCode")
    @JsonProperty("materialCode")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    @Field("materialName")
    @JsonProperty("materialName")
    private String materialName;

    @ApiModelProperty(value = "物料规格")
    @Field("materialSpec")
    @JsonProperty("materialSpec")
    private String materialSpec;

    @ApiModelProperty(value = "供应商名称")
    @Field("supplier")
    @JsonProperty("supplier")
    private String supplier;

    @ApiModelProperty(value = "计量单位")
    @Field("unit")
    @JsonProperty("unit")
    private String unit;

    @ApiModelProperty(value = "BOM标准用量")
    @Field("bomNum")
    @JsonProperty("bomNum")
    private Double bomNum;

    @ApiModelProperty(value = "单包装数量")
    @Field("packageNum")
    @JsonProperty("packageNum")
    private Integer packageNum;

    @ApiModelProperty(value = "物料批次号")
    @Field("materialLot")
    @JsonProperty("materialLot")
    private String materialLot;

    @ApiModelProperty(value = "物料序列号")
    @Field("materialSn")
    @JsonProperty("materialSn")
    private String materialSn;

    @ApiModelProperty(value = "绑定时间")
    @Field("bindTime")
    @JsonProperty("bindTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bindTime;

    @ApiModelProperty(value = "操作员账号")
    @Field("operatorId")
    @JsonProperty("operatorId")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    @Field("operatorName")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "绑定类型（0：正常绑定，1：替换绑定，2：解绑）")
    @Field("bindType")
    @JsonProperty("bindType")
    private Integer bindType;

    @ApiModelProperty(value = "绑定状态（0：已绑定，1：已解绑）")
    @Field("bindStatus")
    @JsonProperty("bindStatus")
    private Integer bindStatus;

    @ApiModelProperty(value = "物料有效期")
    @Field("expireDate")
    @JsonProperty("expireDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireDate;

    @ApiModelProperty(value = "入库时间")
    @Field("inboundTime")
    @JsonProperty("inboundTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime inboundTime;

    @ApiModelProperty(value = "备注信息")
    @Field("remark")
    @JsonProperty("remark")
    private String remark;

    /**
     * 绑定类型枚举
     */
    public enum BindType {
        正常绑定(0, "正常绑定"),
        替换绑定(1, "替换绑定"),
        解绑(2, "解绑");

        private final int code;
        private final String description;

        BindType(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 绑定状态枚举
     */
    public enum BindStatus {
        已绑定(0, "已绑定"),
        已解绑(1, "已解绑");

        private final int code;
        private final String description;

        BindStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}

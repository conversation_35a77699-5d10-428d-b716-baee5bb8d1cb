using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MDC.Models
{

    /// <summary>
    /// 产品异常信息实体类（MongoDB 存储对象）
    /// </summary>
    [BsonIgnoreExtraElements] // 忽略文档中未定义的字段
    public class ProductException
    {
        /// <summary>
        /// MongoDB 文档主键（ObjectId 类型，自动生成）
        /// 格式：ObjectId("507f1f77bcf86cd799439011")
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [JsonProperty("_id")]
        [JsonConverter(typeof(ObjectIdNullableConverter))] // 自定义转换器
        public ObjectId? _id { get; set; }

        /// <summary>
        /// 集合名称
        /// </summary>
        /// <remarks>对应 BSON 文档中的 collection 字段</remarks>
        [BsonElement("collection")]
        [BsonRequired] // 强制字段非空[8](@ref)
        public string collection { get; set; } = "product_exception";

        /// <summary>
        /// 产品序列号（唯一标识）
        /// 示例："B0675A015C633922A087303365"
        /// </summary>
        [BsonElement("sn")]
        [BsonRequired]
        [JsonProperty("sn")]
        public string sn { get; set; } 

        /// <summary>
        /// 产品型号编码
        /// </summary>
        [BsonElement("modelCode")]
        [JsonProperty("ModelCode")]
        public string modelCode { get; set; }

        /// <summary>
        /// 生产工单号
        /// </summary>
        [BsonElement("productionOrder")]
        [JsonProperty("ProductionOrder")]
        public string productionOrder { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        [BsonElement("lineName")]
        [JsonProperty("LineName")]
        public string lineName { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        [BsonElement("lineCode")]
        [JsonProperty("LineCode")]
        public string lineCode { get; set; }

        /// <summary>
        /// 产品状态
        /// 可选值：[直通品/待复判/复判良品/复判返修/复判报废/返修中/返修良品/返修报废]
        /// 示例："直通品"
        /// </summary>
        [BsonElement("status")]
        [JsonProperty("Status")]
        public string status { get; set; } = string.Empty;

        /// <summary>
        /// 不良流程是否已完成
        /// </summary>
        [BsonElement("isResolved")]
        [JsonProperty("isResolved")]
        public bool isResolved { get; set; } = false;

        /// <summary>
        /// 已完成工序列表
        /// </summary>
        public List<string> processLog { get; set; } = new List<string>();

        #region 申报信息
        /// <summary>
        /// 判定类型（0：良品，1：待复判， 2：返修， 3：报废）
        /// <example>L-SMT01</example>
        /// </summary>
        [JsonProperty("reportType")]
        [BsonElement("reportType")]
        public string reportType { get; set; }

        /// <summary>
        /// 不良描述
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("reportFail")]
        [BsonElement("reportFail")]
        public string reportFail { get; set; }

        /// <summary>
        /// 申报不良工序编码
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("reportProcessCode")]
        [BsonElement("reportProcessCode")]
        public string reportProcessCode { get; set; }

        /// <summary>
        /// 工序名称
        /// <example></example>
        /// </summary>
        [JsonProperty("reportProcessName")]
        [BsonElement("reportProcessName")]
        public string reportProcessName { get; set; }

        /// <summary>
        /// 工站IP地址
        /// <example></example>
        /// </summary>
        [JsonProperty("reportStationIP")]
        [BsonElement("reportStationIP")]
        public string reportStationIP { get; set; }

        /// <summary>
        /// 工站名称
        /// <example></example>
        /// </summary>
        [JsonProperty("reportStationName")]
        [BsonElement("reportStationName")]
        public string reportStationName { get; set; }

        /// <summary>
        /// 操作员账号
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("reportOperatorId")]
        [BsonElement("reportOperatorId")]
        public string reportOperatorId { get; set; }

        /// <summary>
        /// 操作员姓名
        /// <example>张三</example>
        /// </summary>
        [JsonProperty("reportOperatorName")]
        [BsonElement("reportOperatorName")]
        public string reportOperatorName { get; set; }

        /// <summary>
        /// 不良申报时间
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2024-05-20T08:31:05.000Z</example>
        /// </summary>
        [JsonProperty("reportTime")]
        [BsonElement("reportTime")]
        public DateTime reportTime { get; set; }

        #endregion 申报信息


        #region 复判信息
        /// <summary>
        /// 判定类型（0：良品，1：待复判， 2：返修， 3：报废）
        /// <example>L-SMT01</example>
        /// </summary>
        [JsonProperty("judgeType")]
        [BsonElement("judgeType")]
        public string judgeType { get; set; }

        /// <summary>
        /// 不良描述
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("judgeFail")]
        [BsonElement("judgeFail")]
        public string judgeFail { get; set; }

        /// <summary>
        /// 申报不良工序编码
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("judgeProcessCode")]
        [BsonElement("judgeProcessCode")]
        public string judgeProcessCode { get; set; }

        /// <summary>
        /// 工序名称
        /// <example></example>
        /// </summary>
        [JsonProperty("judgeProcessName")]
        [BsonElement("judgeProcessName")]
        public string judgeProcessName { get; set; }

        /// <summary>
        /// 工站IP地址
        /// <example></example>
        /// </summary>
        [JsonProperty("judgeStationIP")]
        [BsonElement("judgeStationIP")]
        public string judgeStationIP { get; set; }

        /// <summary>
        /// 工站名称
        /// <example></example>
        /// </summary>
        [JsonProperty("judgeStationName")]
        [BsonElement("judgeStationName")]
        public string judgeStationName { get; set; }

        /// <summary>
        /// 操作员账号
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("judgeOperatorId")]
        [BsonElement("judgeOperatorId")]
        public string judgeOperatorId { get; set; }

        /// <summary>
        /// 操作员姓名
        /// <example>张三</example>
        /// </summary>
        [JsonProperty("judgeOperatorName")]
        [BsonElement("judgeOperatorName")]
        public string judgeOperatorName { get; set; }

        /// <summary>
        /// 不良申报时间
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2024-05-20T08:31:05.000Z</example>
        /// </summary>
        [JsonProperty("judgeTime")]
        [BsonElement("judgeTime")]
        public DateTime judgeTime { get; set; }

        #endregion 申报信息


        #region 返修信息
        /// <summary>
        /// 判定类型（0：良品，1：待复判， 2：返修， 3：报废）
        /// <example>L-SMT01</example>
        /// </summary>
        [JsonProperty("repairType")]
        [BsonElement("repairType")]
        public string repairType { get; set; }

        /// <summary>
        /// 不良描述
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("repairFail")]
        [BsonElement("repairFail")]
        public string repairFail { get; set; }

        /// <summary>
        /// 申报不良工序编码
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("repairProcessCode")]
        [BsonElement("repairProcessCode")]
        public string repairProcessCode { get; set; }

        /// <summary>
        /// 工序名称
        /// <example></example>
        /// </summary>
        [JsonProperty("repairProcessName")]
        [BsonElement("repairProcessName")]
        public string repairProcessName { get; set; }

        /// <summary>
        /// 工站IP地址
        /// <example></example>
        /// </summary>
        [JsonProperty("repairStationIP")]
        [BsonElement("repairStationIP")]
        public string repairStationIP { get; set; }

        /// <summary>
        /// 工站名称
        /// <example></example>
        /// </summary>
        [JsonProperty("repairStationName")]
        [BsonElement("repairStationName")]
        public string repairStationName { get; set; }

        /// <summary>
        /// 操作员账号
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("repairOperatorId")]
        [BsonElement("repairOperatorId")]
        public string repairOperatorId { get; set; }

        /// <summary>
        /// 操作员姓名
        /// <example>张三</example>
        /// </summary>
        [JsonProperty("repairOperatorName")]
        [BsonElement("repairOperatorName")]
        public string repairOperatorName { get; set; }

        /// <summary>
        /// 不良申报时间
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2024-05-20T08:31:05.000Z</example>
        /// </summary>
        [JsonProperty("repairTime")]
        [BsonElement("repairTime")]
        public DateTime repairTime { get; set; }

        #endregion 返修信息

        /// <summary>
        /// 重投工序编码
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("reinputProcessCode")]
        [BsonElement("reinputProcessCode")]
        public string reinputProcessCode { get; set; }

        /// <summary>
        /// 重投工序名称
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("reinputProcessName")]
        [BsonElement("reinputProcessName")]
        public string reinputProcessName { get; set; }

    }


}

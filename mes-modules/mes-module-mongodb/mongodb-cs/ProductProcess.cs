using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MDC
{
    /// <summary>
    /// 产品过站记录模型（对应集合 product_process）
    /// </summary>
    [BsonIgnoreExtraElements] // 忽略文档中未定义的字段
    public class ProductProcess
    {
        [BsonId]
        /// <summary>
        /// 唯一标识符（MongoDB ObjectId）
        /// <example>685e5eaa5cac51d9fb0b68f8</example>
        /// </summary>
        [BsonRepresentation(BsonType.ObjectId)]
        [JsonProperty("_id")]
        public ObjectId? _id { get; set; }

        /// <summary>
        /// 集合名称
        /// </summary>
        /// <remarks>对应 BSON 文档中的 collection 字段</remarks>
        [JsonProperty("collection")]
        [BsonElement("collection")]
        [BsonRequired] // 强制字段非空[8](@ref)
        public string collection { get; set; }

        /// <summary>
        /// 过站时间（必须字段）
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2025-06-27T08:42:25.000Z</example>
        /// </summary>
        [JsonProperty("processTime")]
        [BsonElement("processTime")]
        public DateTime processTime { get; set; }

        /// <summary>
        /// 元数据字段（用于筛选和分组）
        /// <para>包含产线、操作员、工序和产品SN等核心信息</para>
        /// </summary>
        [JsonProperty("meta")]
        [BsonElement("meta")]
        public MetaData meta { get; set; } = new MetaData();

        /// <summary>
        /// 治具版本
        /// <example>V1.0</example>
        /// </summary>
        [JsonProperty("fixtureVersion")]
        [BsonElement("fixtureVersion")]
        public string fixtureVersion { get; set; }

        /// <summary>
        /// 测试结果
        /// <para>取值：PASS/FAIL</para>
        /// </summary>
        [JsonProperty("result")]
        [BsonElement("result")]
        public string result { get; set; }

        /// <summary>
        /// 设备参数集合
        /// <para>包含测试过程中的各项设备参数</para>
        /// <example>{"voltage":12.2,"temp":35.6}</example>
        /// </summary>
        [JsonProperty("parameters")]
        [BsonElement("parameters")]
        public Dictionary<string, object> parameters { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 产品扫描时间
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2024-05-20T08:30:00.000Z</example>
        /// </summary>
        [JsonProperty("scanTime")]
        [BsonElement("scanTime")]
        public DateTime scanTime { get; set; }

        /// <summary>
        /// 产线名称
        /// <example>SMT-01线</example>
        /// </summary>
        [JsonProperty("lineName")]
        [BsonElement("lineName")]
        public string lineName { get; set; }

        /// <summary>
        /// 工站IP地址
        /// <example>**************</example>
        /// </summary>
        [JsonProperty("stationIP")]
        [BsonElement("stationIP")]
        public string stationIP { get; set; }

        /// <summary>
        /// 工站名称
        /// <example>测试工站3号</example>
        /// </summary>
        [JsonProperty("stationName")]
        [BsonElement("stationName")]
        public string stationName { get; set; }

        /// <summary>
        /// 治具名称
        /// <example>FIX-2024-T01</example>
        /// </summary>
        [JsonProperty("fixtureName")]
        [BsonElement("fixtureName")]
        public string fixtureName { get; set; }

        /// <summary>
        /// 数据提交时间
        /// <para>格式：ISO 8601日期时间</para>
        /// <example>2024-05-20T08:31:05.000Z</example>
        /// </summary>
        [JsonProperty("submitTime")]
        [BsonElement("submitTime")]
        public DateTime submitTime { get; set; }

        /// <summary>
        /// 是否为补扫记录
        /// </summary>
        [JsonProperty("isRepairScan")]
        [DefaultValue(false)]
        [BsonElement("isRepairScan")]
        public bool isRepairScan { get; set; }

        /// <summary>
        /// 扫描编码
        /// <example>SCAN-0510-001</example>
        /// </summary>
        [JsonProperty("scanCode")]
        [BsonElement("scanCode")]
        public string scanCode { get; set; }
        /// <summary>
        /// 不良描述列表
        /// <para>包含所有检测到的不良问题描述</para>
        /// <example>["电压不稳", "接触不良"]</example>
        /// </summary>
        [JsonProperty("failDesc")]
        [BsonElement("failDesc")]
        public List<string> failDesc { get; set; } = new List<string>();


        /// <summary>
        /// 不良名称
        /// <para>包含检测到的不良问题描述</para>
        /// <example>["电压不稳", "接触不良"]</example>
        /// </summary>
        [JsonProperty("failName")]
        [BsonElement("failName")]
        public string failName { get; set; }

        /// <summary>
        /// 不良编码
        /// <para>包含检测到的不良问题编码</para>
        /// <example>["电压不稳", "接触不良"]</example>
        /// </summary>
        [JsonProperty("failCode")]
        [BsonElement("failCode")]
        public string failCode { get; set; }


        /// <summary>
        /// 操作员姓名
        /// <example>张三</example>
        /// </summary>
        [JsonProperty("operatorName")]
        [BsonElement("operatorName")]
        public string operatorName { get; set; }

        /// <summary>
        /// 治具编码
        /// <example>FIX-2024-T01</example>
        /// </summary>
        [JsonProperty("fixtureCode")]
        [BsonElement("fixtureCode")]
        public string fixtureCode { get; set; }

        /// <summary>
        /// 工序名称
        /// <example>功能测试</example>
        /// </summary>
        [JsonProperty("processName")]
        [BsonElement("processName")]
        public string processName { get; set; }

        /// <summary>
        /// 是否首道工序
        /// <example>True</example>
        /// </summary>
        [JsonProperty("isFirstProcess")]
        [BsonElement("isFirstProcess")]
        public bool isFirstProcess { get; set; }

        /// <summary>
        /// 重投工序编码
        /// <example>喷码</example>
        /// </summary>
        [JsonProperty("reworkProcessCode")]
        [BsonElement("reworkProcessCode")]
        public string reworkProcessCode { get; set; }

        /// <summary>
        /// 重投工序名称
        /// <example>喷码</example>
        /// </summary>
        [JsonProperty("reworkProcessName")]
        [BsonElement("reworkProcessName")]
        public string reworkProcessName { get; set; }
    }

    /// <summary>
    /// 表示产品过站记录中的元数据
    /// <para>用于数据筛选和分组的关键字段</para>
    /// </summary>
    public class MetaData
    {
        /// <summary>
        /// 产线编码
        /// <example>L-SMT01</example>
        /// </summary>
        [JsonProperty("lineCode")]
        [BsonElement("lineCode")]
        public string lineCode { get; set; }

        /// <summary>
        /// 操作员账号
        /// <example>OP1002</example>
        /// </summary>
        [JsonProperty("operatorId")]
        [BsonElement("operatorId")]
        public string operatorId { get; set; }

        /// <summary>
        /// 工序编码
        /// <example>PROC-020</example>
        /// </summary>
        [JsonProperty("processCode")]
        [BsonElement("processCode")]
        public string processCode { get; set; }

        /// <summary>
        /// 产品序列号
        /// <example>SN202405200001</example>
        /// </summary>
        [JsonProperty("sn")]
        [BsonElement("sn")]
        public string sn { get; set; }
    }
}
